import { enumToArray } from '../script';
import { type TradeChannel } from '../types/trade';

/** 资产类型 */
export enum AssetTypeEnum {
  期货 = 1,
  股票 = 2,
  期权 = 3,
  债券 = 4,
  基金 = 5,
  现货 = 6,
  回购 = 7,
}

/** 资产类型 */
export const ASSET_TYPES = enumToArray(AssetTypeEnum);

/** 交易渠道 */
export const TRADE_CHANNELS: TradeChannel[] = [
  {
    label: '现货竞价',
    name: 'spot',
    assetTypes: [AssetTypeEnum.股票, AssetTypeEnum.基金, AssetTypeEnum.债券],
  },
  {
    label: '融资融券',
    name: 'credit',
    assetTypes: [AssetTypeEnum.股票, AssetTypeEnum.基金, AssetTypeEnum.债券],
    credit: true,
  },
  // {
  //   label: '期货',
  //   name: 'future',
  //   assetTypes: [AssetTypeEnum.期货],
  // },
];

/** 交易方向 */
export enum TradeDirectionEnum {
  买入 = 1,
  卖出 = -1,
}

export const TRADE_DIRECTIONS = enumToArray(TradeDirectionEnum);

/** 价格类型 */
export enum OrderPriceTypeEnum {
  限价 = 1,
  市价 = 2,
  模拟 = 3,
}

/** 对冲标志 */
export enum HedgeFlagEnum {
  Start = 0,
  投机 = 1,
  套利 = 2,
  强平 = 3,
  End = 4,
}

/**
 * 算法执行时间
 */
export enum AlgorithmExecuteTimeEnum {
  立即执行 = 1,
  指定时间 = 2,
  当日有效 = 3,
}

/**
 * 算法执行时间
 */
export const AlgorithmExecuteTimes = enumToArray(AlgorithmExecuteTimeEnum);

/**
 * 到期未成处理
 */
export enum ExpirationUnfinishedTreatmentEnum {
  自动撤销 = 1,
  转为限价 = 2,
}

/**
 * 到期未成处理
 */
export const ExpirationUnfinishedTreatments = enumToArray(ExpirationUnfinishedTreatmentEnum);

/**
 * 交易风格
 */
export enum TradeStyleEnum {
  激进型 = 1,
  稳健型 = 2,
  保守型 = 3,
}

/**
 * 交易风格
 */
export const TradeStyles = enumToArray(TradeStyleEnum);

/**
 * 篮子交易模式
 */
export enum BasketTradeModeEnum {
  按数量 = 1,
  按权重 = 2,
}

/**
 * 篮子交易模式
 */
export const BasketTradeModes = enumToArray(BasketTradeModeEnum);
