<script setup lang="ts">
import { computed, reactive, ref } from 'vue';
import TradeDirection from '@/components/NormalTradeView/TradeTabs/TradeDirection.vue';
import { TradeDirectionEnum } from '@/enum';
import type { BasketTradeInfo } from '@/types/basket';
import { thousands } from '@/script';

import {
  BasketTradeModeEnum,
  BasketTradeModes,
  AlgorithmExecuteTimeEnum,
  AlgorithmExecuteTimes,
  ExpirationUnfinishedTreatmentEnum,
  ExpirationUnfinishedTreatments,
  TradeStyleEnum,
  TradeStyles,
} from '@/enum/trade';

const direction = ref<TradeDirectionEnum>(TradeDirectionEnum.买入);
const isBuy = computed(() => direction.value === TradeDirectionEnum.买入);
const isByBaskets = computed(() => basketForm.tradeMode === BasketTradeModeEnum.按数量);
const isByWeight = computed(() => !isByBaskets.value);

const basketForm = reactive<BasketTradeInfo>({
  basketId: null as any,
  tradeMode: BasketTradeModeEnum.按数量,
  targetAmount: 0,
  targetBaskets: 0,
  algorithmId: null as any,
  executionTime: AlgorithmExecuteTimeEnum.立即执行,
  timeRange: [new Date(), new Date()],
  volumeRatio: 0,
  openingCallAuction: false,
  openingCallAuctionParticipation: 0,
  openingCallAuctionPriceOffset: 0,
  maxPrice: 0,
  unfinishedTreatment: ExpirationUnfinishedTreatmentEnum.自动撤销,
  tradingStyle: TradeStyleEnum.保守型,
  cancellationRate: 0,
  minSingleVolume: 0,
  investmentNotes: '',
});

const baskets = ref<{ id: number; name: string }[]>([]);
const algorithms = ref<{ id: number; name: string }[]>([]);

const volumeStep = computed(() => {
  return 100;
});

const isExecutedByTimeRange = computed(() => {
  return basketForm.executionTime == AlgorithmExecuteTimeEnum.指定时间;
});

/**
 * 预估金额
 */
const estimatedAmount = computed(() => {
  return 99999999.99;
});
</script>

<template>
  <div class="trading-panel" h-full flex flex-col of-y-hidden>
    <div h-500 flex-1 p-l-20 p-r-20 of-y-auto>
      <!-- 买卖方向 -->
      <div class="direction-section" p-16>
        <TradeDirection v-model="direction" />
      </div>

      <!-- 篮子交易表单 -->
      <el-form :model="basketForm" label-width="160px" label-position="left" class="basket-form">
        <el-form-item label="篮子选择">
          <el-select v-model="basketForm.basketId" placeholder="请选择篮子">
            <el-option v-for="item in baskets" :key="item.id" :label="item.name" :value="item.id" />
          </el-select>
        </el-form-item>

        <el-form-item label="交易模式">
          <el-select v-model="basketForm.tradeMode" placeholder="请选择交易模式">
            <el-option
              v-for="item in BasketTradeModes"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item v-if="isByBaskets" label="篮子数量">
          <el-input-number
            :controls="false"
            v-model="basketForm.targetBaskets"
            :min="0"
            :precision="0"
            :step="1"
          >
            <template #suffix>
              <span>篮</span>
            </template>
          </el-input-number>
        </el-form-item>

        <el-form-item v-if="isByWeight" label="目标金额">
          <el-input-number
            :controls="false"
            v-model="basketForm.targetAmount"
            :min="0"
            :precision="0"
          >
            <template #suffix>
              <span>元</span>
            </template>
          </el-input-number>
        </el-form-item>

        <el-form-item label="交易算法">
          <el-select v-model="basketForm.algorithmId" placeholder="请选择算法">
            <el-option
              v-for="item in algorithms"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="时间类型">
          <el-select v-model="basketForm.executionTime" placeholder="请选择时间类型">
            <el-option
              v-for="item in AlgorithmExecuteTimes"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item v-if="isExecutedByTimeRange" label="起止时间">
          <el-time-picker
            v-model="basketForm.timeRange"
            range-separator="至"
            start-placeholder="开始"
            end-placeholder="结束"
            format="HH:mm:ss"
            is-range
          />
        </el-form-item>

        <el-form-item label="量比比例">
          <el-input-number
            :controls="false"
            v-model="basketForm.volumeRatio"
            :precision="2"
            :step="0.01"
            :min="0"
            :max="100"
          >
            <template #suffix>
              <span>%</span>
            </template>
          </el-input-number>
        </el-form-item>

        <el-form-item label="开盘集合竞价">
          <el-switch v-model="basketForm.openingCallAuction" />
        </el-form-item>

        <el-form-item v-if="basketForm.openingCallAuction" label="开盘集合竞价参与比例">
          <el-input-number
            :controls="false"
            v-model="basketForm.openingCallAuctionParticipation"
            :precision="2"
            :step="0.01"
            :min="0"
            :max="100"
          >
            <template #suffix>
              <span>%</span>
            </template>
          </el-input-number>
        </el-form-item>

        <el-form-item v-if="basketForm.openingCallAuction" label="开盘集合竞价价格偏移">
          <el-input-number
            :controls="false"
            v-model="basketForm.openingCallAuctionPriceOffset"
            :precision="2"
            :step="0.01"
            :min="0"
          >
            <template #suffix>
              <span>元</span>
            </template>
          </el-input-number>
        </el-form-item>

        <el-form-item label="触价设置">
          <el-input-number
            :controls="false"
            v-model="basketForm.maxPrice"
            :precision="2"
            :step="0.01"
            :min="0"
            :max="100"
          >
            <template #prefix>
              <span>最新价&lt;=</span>
            </template>
            <template #suffix>
              <span>%</span>
            </template>
          </el-input-number>
        </el-form-item>

        <el-form-item label="到期未成处理">
          <el-select v-model="basketForm.unfinishedTreatment" placeholder="请选择处理方式">
            <el-option
              v-for="item in ExpirationUnfinishedTreatments"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="交易风格">
          <el-select v-model="basketForm.tradingStyle" placeholder="请选择交易风格">
            <el-option
              v-for="item in TradeStyles"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="撤单率">
          <el-input-number
            :controls="false"
            v-model="basketForm.cancellationRate"
            :precision="2"
            :step="0.01"
            :min="0"
            :max="100"
          >
            <template #suffix>
              <span>%</span>
            </template>
          </el-input-number>
        </el-form-item>

        <el-form-item label="单笔最小量">
          <el-input-number
            :controls="false"
            v-model="basketForm.minSingleVolume"
            :min="0"
            :precision="0"
            :step="volumeStep"
          >
            <template #suffix>
              <span>股</span>
            </template>
          </el-input-number>
        </el-form-item>

        <el-form-item label="投资备注">
          <el-input
            v-model.trim="basketForm.investmentNotes"
            :maxlength="100"
            placeholder="请输入投资备注"
            clearable
          />
        </el-form-item>

        <el-form-item label="预估金额">
          <div w-full text-right>{{ thousands(estimatedAmount) }}</div>
        </el-form-item>
      </el-form>
    </div>
    <div class="action-button" h-47 lh-47 fs-14 fw-500 text-center flex gap-10>
      <div class="button-inner" :class="{ buy: isBuy, sell: !isBuy }" w-100 flex-1 h-full>
        {{ isBuy ? '买入试算' : '卖出试算' }}
      </div>
      <div class="button-inner" :class="{ buy: isBuy, sell: !isBuy }" w-100 flex-1 h-full>
        {{ isBuy ? '买入' : '卖出' }}
      </div>
    </div>
  </div>
</template>

<style scoped>
.trading-panel {
  :deep() {
    .el-input-number,
    .el-select {
      width: 100%;
    }
    .el-input__inner {
      text-align: left;
    }
  }

  .action-button {
    .button-inner {
      &.buy {
        background-color: var(--g-red-2);
      }
      &.sell {
        background-color: var(--g-bg-green);
      }
    }
  }
}
</style>
