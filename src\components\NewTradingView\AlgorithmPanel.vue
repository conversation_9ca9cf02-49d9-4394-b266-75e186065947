<script setup lang="ts">
import { computed, reactive, ref } from 'vue';
import InstrumentInput from '@/components/common/InstrumentInput.vue';
import TradeDirection from '@/components/NormalTradeView/TradeTabs/TradeDirection.vue';
import type { AccountInfo, InstrumentInfo } from '@/types';
import { TradeDirectionEnum } from '@/enum';
import type { AlgorithmTradeInfo } from '@/types/algo';
import { thousands } from '@/script';

import {
  AlgorithmExecuteTimeEnum,
  AlgorithmExecuteTimes,
  AssetTypeEnum,
  ExpirationUnfinishedTreatmentEnum,
  ExpirationUnfinishedTreatments,
  TradeStyleEnum,
  TradeStyles,
} from '@/enum/trade';

const { selectedAccount } = defineProps<{
  selectedAccount?: AccountInfo;
}>();

const direction = ref<TradeDirectionEnum>(TradeDirectionEnum.买入);
const selectedInstrument = defineModel<InstrumentInfo>('instrument');
const isBuy = computed(() => direction.value === TradeDirectionEnum.买入);
const isStock = computed(() => selectedInstrument.value?.assetType === AssetTypeEnum.股票);
const isBond = computed(() => selectedInstrument.value?.assetType === AssetTypeEnum.债券);
const isOther = computed(() => !isStock.value && !isBond.value);

const volumeUnit = computed(() => {
  return isStock.value ? '股' : isBond.value ? '张' : isOther.value ? '手' : '';
});

const algorithmForm = reactive<AlgorithmTradeInfo>({
  maxVolume: *********,
  volume: 0,
  algorithmId: null as any,
  executionTime: AlgorithmExecuteTimeEnum.立即执行,
  timeRange: [new Date(), new Date()],
  volumeRatio: 0,
  openingCallAuction: false,
  openingCallAuctionParticipation: 0,
  openingCallAuctionPriceOffset: 0,
  maxPrice: 0,
  unfinishedTreatment: ExpirationUnfinishedTreatmentEnum.自动撤销,
  tradingStyle: TradeStyleEnum.保守型,
  cancellationRate: 0,
  minSingleVolume: 0,
  investmentNotes: '',
});

const algorithms = ref<{ id: number; name: string }[]>([]);

const volumeStep = computed(() => {
  return 100;
});

const isExecutedByTimeRange = computed(() => {
  return algorithmForm.executionTime == AlgorithmExecuteTimeEnum.指定时间;
});

/**
 * 预估金额
 */
const estimatedAmount = computed(() => {
  return 99999999.99;
});
</script>

<template>
  <div class="trading-panel" h-full flex flex-col of-y-hidden>
    <div h-500 flex-1 p-l-20 p-r-20 of-y-auto>
      <!-- 买卖方向 -->
      <div class="direction-section" p-16>
        <TradeDirection v-model="direction" />
      </div>
      <!-- 算法交易表单 -->
      <el-form
        :model="algorithmForm"
        label-width="160px"
        label-position="left"
        class="algorithm-form"
      >
        <el-form-item label="代码">
          <InstrumentInput
            v-model="selectedInstrument"
            :asset-type="selectedAccount?.assetType"
            placeholder="请输入合约代码或名称"
          />
        </el-form-item>

        <el-form-item :label="'最大可' + (isBuy ? '买' : '卖')">
          <el-input-number :controls="false" v-model="algorithmForm.maxVolume" :min="0">
            <template #suffix>
              <span>{{ volumeUnit }}</span>
            </template>
          </el-input-number>
        </el-form-item>

        <el-form-item label="数量">
          <el-input-number
            :controls="false"
            v-model="algorithmForm.volume"
            :min="0"
            :precision="0"
            :step="volumeStep"
            :max="algorithmForm.maxVolume"
          >
            <template #suffix>
              <span>{{ volumeUnit }}</span>
            </template>
          </el-input-number>
        </el-form-item>

        <el-form-item label="交易算法">
          <el-select v-model="algorithmForm.algorithmId" placeholder="请选择算法">
            <el-option
              v-for="item in algorithms"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="时间类型">
          <el-select v-model="algorithmForm.executionTime" placeholder="请选择时间类型">
            <el-option
              v-for="item in AlgorithmExecuteTimes"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item v-if="isExecutedByTimeRange" label="起止时间">
          <el-time-picker
            v-model="algorithmForm.timeRange"
            range-separator="至"
            start-placeholder="开始"
            end-placeholder="结束"
            format="HH:mm:ss"
            is-range
          />
        </el-form-item>

        <el-form-item label="量比比例">
          <el-input-number
            :controls="false"
            v-model="algorithmForm.volumeRatio"
            :precision="2"
            :step="0.01"
            :min="0"
            :max="100"
          >
            <template #suffix>
              <span>%</span>
            </template>
          </el-input-number>
        </el-form-item>

        <el-form-item label="开盘集合竞价">
          <el-switch v-model="algorithmForm.openingCallAuction" />
        </el-form-item>

        <el-form-item v-if="algorithmForm.openingCallAuction" label="开盘集合竞价参与比例">
          <el-input-number
            :controls="false"
            v-model="algorithmForm.openingCallAuctionParticipation"
            :precision="2"
            :step="0.01"
            :min="0"
            :max="100"
          >
            <template #suffix>
              <span>%</span>
            </template>
          </el-input-number>
        </el-form-item>

        <el-form-item v-if="algorithmForm.openingCallAuction" label="开盘集合竞价价格偏移">
          <el-input-number
            :controls="false"
            v-model="algorithmForm.openingCallAuctionPriceOffset"
            :precision="2"
            :step="0.01"
            :min="0"
          >
            <template #suffix>
              <span>元</span>
            </template>
          </el-input-number>
        </el-form-item>

        <el-form-item label="触价设置">
          <el-input-number
            :controls="false"
            v-model="algorithmForm.maxPrice"
            :precision="2"
            :step="0.01"
            :min="0"
            :max="100"
          >
            <template #prefix>
              <span>最新价&lt;=</span>
            </template>
            <template #suffix>
              <span>%</span>
            </template>
          </el-input-number>
        </el-form-item>

        <el-form-item label="到期未成处理">
          <el-select v-model="algorithmForm.unfinishedTreatment" placeholder="请选择处理方式">
            <el-option
              v-for="item in ExpirationUnfinishedTreatments"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="交易风格">
          <el-select v-model="algorithmForm.tradingStyle" placeholder="请选择交易风格">
            <el-option
              v-for="item in TradeStyles"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="撤单率">
          <el-input-number
            :controls="false"
            v-model="algorithmForm.cancellationRate"
            :precision="2"
            :step="0.01"
            :min="0"
            :max="100"
          >
            <template #suffix>
              <span>%</span>
            </template>
          </el-input-number>
        </el-form-item>

        <el-form-item label="单笔最小量">
          <el-input-number
            :controls="false"
            v-model="algorithmForm.minSingleVolume"
            :min="0"
            :precision="0"
            :step="volumeStep"
            :max="algorithmForm.maxVolume"
          >
            <template #suffix>
              <span>{{ volumeUnit }}</span>
            </template>
          </el-input-number>
        </el-form-item>

        <el-form-item label="投资备注">
          <el-input
            v-model.trim="algorithmForm.investmentNotes"
            :maxlength="100"
            placeholder="请输入投资备注"
            clearable
          />
        </el-form-item>

        <el-form-item label="预估金额">
          <div w-full text-right>{{ thousands(estimatedAmount) }}</div>
        </el-form-item>
      </el-form>
    </div>
    <div class="action-button" h-47 lh-47 fs-14 fw-500 text-center>
      <div class="button-inner" :class="{ buy: isBuy, sell: !isBuy }" h-full>
        {{ isBuy ? '买入' : '卖出' }}
      </div>
    </div>
  </div>
</template>

<style scoped>
.trading-panel {
  :deep() {
    .el-input-number,
    .el-select {
      width: 100%;
    }
    .el-input__inner {
      text-align: left;
    }
  }

  .action-button {
    .button-inner {
      &.buy {
        background-color: var(--g-red-2);
      }
      &.sell {
        background-color: var(--g-bg-green);
      }
    }
  }
}
</style>
